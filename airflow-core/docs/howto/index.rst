 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.



How-to Guides
=============

Setting up the sandbox in the :doc:`/start` section was easy;
building a production-grade environment requires a bit more work!

These how-to guides will step you through common tasks in using and
configuring an Airflow environment.

.. toctree::
    :maxdepth: 2

    Using the CLI <usage-cli>
    Using the REST API <../security/api>
    add-dag-tags
    notifications
    set-config
    set-up-database
    operator/index
    timetable
    custom-view-plugin
    ui-plugin-development
    listener-plugin
    customize-ui
    custom-operator
    create-custom-decorator
    export-more-env-vars
    connection
    variable
    setup-and-teardown
    run-behind-proxy
    run-with-systemd
    define-extra-link
    email-config
    dynamic-dag-generation
    docker-compose/index
