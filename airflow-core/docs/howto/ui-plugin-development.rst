 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.


Comprehensive UI Plugin Development Guide
=========================================

This guide provides detailed instructions for developing, building, and deploying custom UI plugins for Airflow 3.
UI plugins allow you to extend the Airflow web interface with custom pages, dashboards, and tools that integrate
seamlessly with the main Airflow UI.

Prerequisites
-------------

Before developing UI plugins, ensure you have:

* Airflow 3.0+ installed
* Python 3.8+ with FastAPI knowledge
* Basic understanding of web development (HTML/CSS/JavaScript)
* Optional: Node.js and npm for advanced frontend development

Development Environment Setup
-----------------------------

Setting up a Plugin Development Workspace
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a dedicated workspace for plugin development:

.. code-block:: bash

    mkdir airflow-plugins
    cd airflow-plugins
    
    # Create a virtual environment for plugin development
    python -m venv plugin-dev-env
    source plugin-dev-env/bin/activate  # On Windows: plugin-dev-env\Scripts\activate
    
    # Install Airflow and development dependencies
    pip install apache-airflow[postgres]==3.1.0
    pip install fastapi uvicorn

Plugin Project Structure
^^^^^^^^^^^^^^^^^^^^^^^^

Use this recommended structure for your plugin projects:

.. code-block:: bash

    my_plugin_project/
    ├── pyproject.toml              # Python package configuration
    ├── README.md                   # Plugin documentation
    ├── src/
    │   └── my_plugin/
    │       ├── __init__.py
    │       ├── plugin.py           # Main plugin registration
    │       ├── api/
    │       │   ├── __init__.py
    │       │   └── main.py         # FastAPI application
    │       └── ui/                 # Frontend assets
    │           ├── package.json    # For React/Vue projects
    │           ├── src/            # Source files
    │           └── dist/           # Built assets
    └── tests/                      # Plugin tests

Creating a Plugin Package
-------------------------

Package Configuration
^^^^^^^^^^^^^^^^^^^^^

Create ``pyproject.toml`` for your plugin package:

.. code-block:: toml

    [build-system]
    requires = ["hatchling"]
    build-backend = "hatchling.build"

    [project]
    name = "my-airflow-plugin"
    version = "1.0.0"
    description = "My custom Airflow UI plugin"
    authors = [{name = "Your Name", email = "<EMAIL>"}]
    dependencies = [
        "apache-airflow>=3.0.0",
        "fastapi>=0.100.0",
    ]

    [project.entry-points."airflow.plugins"]
    my_plugin = "my_plugin.plugin:MyUIPlugin"

    [tool.hatch.build.targets.wheel]
    packages = ["src/my_plugin"]

    [tool.hatch.build.targets.wheel.sources]
    "src" = ""

Backend Development
-------------------

FastAPI Application Structure
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a well-structured FastAPI application in ``src/my_plugin/api/main.py``:

.. code-block:: python

    from fastapi import FastAPI, HTTPException, Depends
    from fastapi.staticfiles import StaticFiles
    from fastapi.middleware.cors import CORSMiddleware
    from pathlib import Path
    from typing import Dict, Any
    import logging

    logger = logging.getLogger(__name__)

    # Create FastAPI app
    app = FastAPI(
        title="My Plugin API",
        description="Custom Airflow plugin API",
        version="1.0.0"
    )

    # Add CORS middleware for development
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Serve static UI files
    ui_dist = Path(__file__).parents[1] / "ui" / "dist"
    if ui_dist.exists():
        app.mount("/ui", StaticFiles(directory=ui_dist, html=True), name="ui")
    else:
        logger.warning(f"UI dist directory not found: {ui_dist}")

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "my_plugin"}

    # Plugin-specific API endpoints
    @app.get("/api/data")
    async def get_data():
        """Get plugin data."""
        return {
            "message": "Hello from my plugin!",
            "data": [1, 2, 3, 4, 5],
            "timestamp": "2025-01-01T00:00:00Z"
        }

    @app.post("/api/data")
    async def create_data(data: Dict[str, Any]):
        """Create new data entry."""
        # Implement your data creation logic here
        return {"message": "Data created successfully", "data": data}

    @app.get("/api/config")
    async def get_config():
        """Get plugin configuration."""
        return {
            "plugin_name": "my_plugin",
            "version": "1.0.0",
            "features": ["dashboard", "data_management"]
        }

Plugin Registration
^^^^^^^^^^^^^^^^^^^

Create the plugin registration in ``src/my_plugin/plugin.py``:

.. code-block:: python

    from airflow.plugins_manager import AirflowPlugin
    from .api.main import app

    class MyUIPlugin(AirflowPlugin):
        name = "my_ui_plugin"
        
        # Register FastAPI app
        fastapi_apps = [
            {
                "app": app,
                "url_prefix": "/my_plugin",
                "name": "my_plugin_api"
            }
        ]
        
        # Register UI pages
        ui = [
            {
                "slug": "my-dashboard",
                "label": "My Dashboard",
                "icon": "FiBarChart3",
                "entry": "/my_plugin/ui",
                "type": "iframe",
                "permissions": ["plugins.can_read"],
            },
            {
                "slug": "my-settings",
                "label": "Plugin Settings",
                "icon": "FiSettings",
                "entry": "/my_plugin/ui/settings",
                "type": "iframe",
                "permissions": ["plugins.can_edit"],
            }
        ]

Frontend Development
--------------------

Simple HTML/CSS/JS Approach
^^^^^^^^^^^^^^^^^^^^^^^^^^^

For simple plugins, create static HTML files in ``src/my_plugin/ui/``:

.. code-block:: html

    <!-- src/my_plugin/ui/index.html -->
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>My Plugin Dashboard</title>
        <link rel="stylesheet" href="styles.css">
    </head>
    <body>
        <div class="container">
            <header>
                <h1>My Plugin Dashboard</h1>
                <p>Welcome to your custom Airflow plugin!</p>
            </header>
            
            <main>
                <section class="stats">
                    <div class="stat-card">
                        <h3>Status</h3>
                        <div id="status">Loading...</div>
                    </div>
                    <div class="stat-card">
                        <h3>Data Count</h3>
                        <div id="data-count">Loading...</div>
                    </div>
                </section>
                
                <section class="data-section">
                    <h2>Recent Data</h2>
                    <div id="data-list">Loading...</div>
                </section>
            </main>
        </div>
        
        <script src="app.js"></script>
    </body>
    </html>

Add modern styling in ``src/my_plugin/ui/styles.css``:

.. code-block:: css

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #f8f9fa;
        color: #333;
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .data-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

Add JavaScript functionality in ``src/my_plugin/ui/app.js``:

.. code-block:: javascript

    class PluginApp {
        constructor() {
            this.baseUrl = '/my_plugin';
            this.init();
        }

        async init() {
            await this.loadStatus();
            await this.loadData();
        }

        async loadStatus() {
            try {
                const response = await fetch(`${this.baseUrl}/health`);
                const data = await response.json();
                document.getElementById('status').textContent = data.status;
            } catch (error) {
                console.error('Error loading status:', error);
                document.getElementById('status').textContent = 'Error';
            }
        }

        async loadData() {
            try {
                const response = await fetch(`${this.baseUrl}/api/data`);
                const data = await response.json();
                
                // Update data count
                document.getElementById('data-count').textContent = data.data.length;
                
                // Update data list
                const dataList = document.getElementById('data-list');
                dataList.innerHTML = `
                    <p><strong>Message:</strong> ${data.message}</p>
                    <p><strong>Data:</strong> ${data.data.join(', ')}</p>
                    <p><strong>Timestamp:</strong> ${data.timestamp}</p>
                `;
            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('data-count').textContent = 'Error';
                document.getElementById('data-list').textContent = 'Error loading data';
            }
        }
    }

    // Initialize app when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        new PluginApp();
    });

Building and Testing
--------------------

Local Development
^^^^^^^^^^^^^^^^^

For local development and testing:

.. code-block:: bash

    # Install your plugin in development mode
    cd my_plugin_project
    pip install -e .
    
    # Copy plugin to Airflow plugins directory (alternative approach)
    cp -r src/my_plugin $AIRFLOW_HOME/plugins/
    
    # Start Airflow webserver
    airflow webserver --port 8080
    
    # In another terminal, start scheduler
    airflow scheduler

Building for Distribution
^^^^^^^^^^^^^^^^^^^^^^^^^

Build your plugin for distribution:

.. code-block:: bash

    # Build the package
    python -m build
    
    # This creates:
    # dist/my_airflow_plugin-1.0.0-py3-none-any.whl
    # dist/my_airflow_plugin-1.0.0.tar.gz

Installation and Deployment
---------------------------

Installing the Plugin
^^^^^^^^^^^^^^^^^^^^^

Install your plugin in an Airflow environment:

.. code-block:: bash

    # Install from wheel file
    pip install my_airflow_plugin-1.0.0-py3-none-any.whl
    
    # Or install from source
    pip install .
    
    # Restart Airflow webserver
    airflow webserver --port 8080

Verifying Installation
^^^^^^^^^^^^^^^^^^^^^

Check that your plugin is loaded correctly:

.. code-block:: bash

    # List all plugins
    airflow plugins
    
    # Check for your plugin in the output
    # Look for "my_ui_plugin" in the list

Testing Your Plugin
^^^^^^^^^^^^^^^^^^^

1. Navigate to the Airflow UI (http://localhost:8080)
2. Look for your plugin menu items in the sidebar
3. Click on "My Dashboard" to test the main plugin page
4. Verify that data loads correctly from your API endpoints
5. Test permissions by logging in with different user roles

Best Practices
--------------

Security Considerations
^^^^^^^^^^^^^^^^^^^^^^

* Always validate user permissions in your API endpoints
* Use HTTPS in production environments
* Sanitize user inputs to prevent XSS attacks
* Implement proper authentication for sensitive operations

Performance Optimization
^^^^^^^^^^^^^^^^^^^^^^^^

* Minimize the size of your UI assets
* Use caching for frequently accessed data
* Implement pagination for large datasets
* Consider using a CDN for static assets in production

Error Handling
^^^^^^^^^^^^^^

* Implement comprehensive error handling in both frontend and backend
* Provide meaningful error messages to users
* Log errors appropriately for debugging
* Include fallback UI states for when data fails to load

Troubleshooting
---------------

Common Issues and Solutions
^^^^^^^^^^^^^^^^^^^^^^^^^^

**Plugin not appearing in sidebar:**

* Verify the plugin is installed correctly: ``pip list | grep my-airflow-plugin``
* Check Airflow logs for plugin loading errors
* Ensure the webserver was restarted after installation
* Verify the ``ui`` configuration in your plugin class

**UI not loading:**

* Check that static files are being served correctly
* Verify the ``entry`` URL matches your FastAPI mount point
* Test the plugin URL directly in a browser
* Check browser console for JavaScript errors

**API endpoints not working:**

* Verify FastAPI app is registered correctly in ``fastapi_apps``
* Check the URL prefix configuration
* Test API endpoints directly (e.g., ``/my_plugin/api/data``)
* Review FastAPI logs for errors

**Permission issues:**

* Verify the permissions exist in your Airflow installation
* Check that users have the required permissions
* Test with an admin user to isolate permission issues
* Review the Airflow security configuration

Next Steps
----------

After successfully creating your first UI plugin, consider:

* Adding more sophisticated frontend frameworks (React, Vue.js)
* Implementing real-time updates with WebSockets
* Adding database integration for persistent data
* Creating reusable plugin templates for your organization
* Contributing your plugin to the Airflow community

For more advanced topics and examples, see:

* :doc:`/howto/custom-view-plugin` - Basic plugin creation guide
* :doc:`/administration-and-deployment/plugins` - Plugin system overview
* `Airflow Plugin Examples <https://github.com/apache/airflow/tree/main/airflow/example_dags>`_ - Official examples
