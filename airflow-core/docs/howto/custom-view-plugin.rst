 .. Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

 ..   http://www.apache.org/licenses/LICENSE-2.0

 .. Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.


Creating Custom UI Plugins for Airflow
=======================================

Airflow 3 provides comprehensive support for custom UI plugins that integrate seamlessly with the main Airflow web interface.
UI plugins allow you to add custom pages, dashboards, and tools that appear as menu items in the Airflow sidebar navigation
while maintaining the consistent look and feel of the Airflow UI.

Overview
--------

UI plugins in Airflow 3 consist of two main components:

1. **Backend (FastAPI)**: Serves your plugin's API endpoints and static UI files
2. **Frontend Registration**: Declares your plugin's UI pages in the Airflow plugin system

When a UI plugin is registered, Airflow automatically:

* Adds menu items to the sidebar navigation
* Routes plugin URLs to your FastAPI application
* Handles user permissions and access control
* Renders your plugin's UI within the main Airflow layout

Plugin Architecture
-------------------

UI plugins use an iframe-based architecture that provides several benefits:

* **Isolation**: Your plugin's code runs independently from the main Airflow UI
* **Technology Freedom**: Use any frontend framework (React, Vue.js, Angular, or plain HTML/JS)
* **Theme Integration**: Automatically inherits Airflow's theme and styling
* **Security**: Proper isolation between plugin and core UI

Creating a UI Plugin
--------------------

Step 1: Create the Plugin Structure
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a directory structure for your plugin:

.. code-block:: bash

    my_plugin/
    ├── __init__.py
    ├── plugin.py          # Plugin registration
    ├── api.py             # FastAPI application
    └── ui/                # Frontend code
        ├── index.html
        ├── style.css
        └── script.js

Step 2: Implement the FastAPI Backend
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create ``api.py`` to serve your plugin's API and static files:

.. code-block:: python

    # api.py
    from fastapi import FastAPI
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse
    from pathlib import Path

    app = FastAPI(title="My Plugin API")

    # Serve static UI files
    ui_dir = Path(__file__).parent / "ui"
    app.mount("/ui", StaticFiles(directory=ui_dir, html=True), name="ui")

    # Add API endpoints for your plugin
    @app.get("/api/data")
    async def get_data():
        return {"message": "Hello from my plugin!", "data": [1, 2, 3]}

    @app.get("/api/status")
    async def get_status():
        return {"status": "healthy", "version": "1.0.0"}

Step 3: Create the Frontend UI
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create a simple HTML interface in ``ui/index.html``:

.. code-block:: html

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>My Plugin</title>
        <link rel="stylesheet" href="style.css">
    </head>
    <body>
        <div class="container">
            <h1>My Custom Plugin</h1>
            <p>Welcome to my custom Airflow plugin!</p>

            <div class="card">
                <h2>Plugin Status</h2>
                <div id="status">Loading...</div>
            </div>

            <div class="card">
                <h2>Data</h2>
                <div id="data">Loading...</div>
            </div>
        </div>

        <script src="script.js"></script>
    </body>
    </html>

Add styling in ``ui/style.css``:

.. code-block:: css

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
    }

    .container {
        max-width: 800px;
        margin: 0 auto;
    }

    .card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    h1 {
        color: #333;
        border-bottom: 2px solid #007acc;
        padding-bottom: 10px;
    }

    h2 {
        color: #555;
        margin-top: 0;
    }

Add JavaScript functionality in ``ui/script.js``:

.. code-block:: javascript

    // script.js
    async function loadData() {
        try {
            // Load status
            const statusResponse = await fetch('/my_plugin/api/status');
            const status = await statusResponse.json();
            document.getElementById('status').innerHTML =
                `<strong>Status:</strong> ${status.status}<br>
                 <strong>Version:</strong> ${status.version}`;

            // Load data
            const dataResponse = await fetch('/my_plugin/api/data');
            const data = await dataResponse.json();
            document.getElementById('data').innerHTML =
                `<strong>Message:</strong> ${data.message}<br>
                 <strong>Data:</strong> ${JSON.stringify(data.data)}`;
        } catch (error) {
            console.error('Error loading data:', error);
            document.getElementById('status').innerHTML = 'Error loading status';
            document.getElementById('data').innerHTML = 'Error loading data';
        }
    }

    // Load data when page loads
    document.addEventListener('DOMContentLoaded', loadData);

Step 4: Register the Plugin
^^^^^^^^^^^^^^^^^^^^^^^^^^^

Create ``plugin.py`` to register your plugin with Airflow:

.. code-block:: python

    # plugin.py
    from airflow.plugins_manager import AirflowPlugin
    from .api import app

    class MyUIPlugin(AirflowPlugin):
        name = "my_ui_plugin"

        # Register the FastAPI app
        fastapi_apps = [
            {
                "app": app,
                "url_prefix": "/my_plugin",
                "name": "my_plugin_api"
            }
        ]

        # Register the UI plugin
        ui = [
            {
                "slug": "my-plugin",
                "label": "My Plugin",
                "icon": "FiZap",
                "entry": "/my_plugin/ui",
                "type": "iframe",
                "permissions": ["plugins.can_read"],
            }
        ]

Step 5: Install and Test
^^^^^^^^^^^^^^^^^^^^^^^^

1. Copy your plugin directory to ``$AIRFLOW_HOME/plugins/``
2. Restart the Airflow webserver
3. Navigate to the Airflow UI
4. Look for "My Plugin" in the sidebar navigation
5. Click it to see your custom plugin page

Advanced Features
-----------------

Using React with Chakra UI
^^^^^^^^^^^^^^^^^^^^^^^^^^

For more sophisticated UIs, you can use React with Chakra UI to match Airflow's design system:

.. code-block:: bash

    # In your ui/ directory
    npm create vite@latest . -- --template react-ts
    npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion

Create a React component that matches Airflow's theme:

.. code-block:: tsx

    // ui/src/App.tsx
    import { ChakraProvider, Box, Heading, Text, VStack, Card, CardBody } from '@chakra-ui/react';
    import { useEffect, useState } from 'react';

    function App() {
        const [status, setStatus] = useState(null);
        const [data, setData] = useState(null);

        useEffect(() => {
            // Load data from your API
            fetch('/my_plugin/api/status')
                .then(res => res.json())
                .then(setStatus);

            fetch('/my_plugin/api/data')
                .then(res => res.json())
                .then(setData);
        }, []);

        return (
            <ChakraProvider>
                <Box p={5}>
                    <Heading mb={5}>My Custom Plugin</Heading>

                    <VStack spacing={4} align="stretch">
                        <Card>
                            <CardBody>
                                <Heading size="md" mb={3}>Status</Heading>
                                {status ? (
                                    <Text>Status: {status.status}, Version: {status.version}</Text>
                                ) : (
                                    <Text>Loading...</Text>
                                )}
                            </CardBody>
                        </Card>

                        <Card>
                            <CardBody>
                                <Heading size="md" mb={3}>Data</Heading>
                                {data ? (
                                    <Text>{data.message}</Text>
                                ) : (
                                    <Text>Loading...</Text>
                                )}
                            </CardBody>
                        </Card>
                    </VStack>
                </Box>
            </ChakraProvider>
        );
    }

    export default App;

Build your React app and update your FastAPI app to serve the built files:

.. code-block:: bash

    cd ui && npm run build

.. code-block:: python

    # Update api.py to serve built React app
    ui_dist = Path(__file__).parent / "ui" / "dist"
    app.mount("/ui", StaticFiles(directory=ui_dist, html=True), name="ui")

Permissions and Security
^^^^^^^^^^^^^^^^^^^^^^^^

Control access to your plugin using permissions:

.. code-block:: python

    ui = [
        {
            "slug": "admin-plugin",
            "label": "Admin Tools",
            "icon": "FiSettings",
            "entry": "/my_plugin/admin",
            "type": "iframe",
            "permissions": ["can_edit", "admin.can_read"],  # Multiple permissions
        }
    ]

Users must have ALL specified permissions to see the menu item.

Multiple Pages
^^^^^^^^^^^^^^

A single plugin can register multiple UI pages:

.. code-block:: python

    ui = [
        {
            "slug": "dashboard",
            "label": "Dashboard",
            "icon": "FiBarChart3",
            "entry": "/my_plugin/dashboard",
            "type": "iframe",
        },
        {
            "slug": "settings",
            "label": "Settings",
            "icon": "FiSettings",
            "entry": "/my_plugin/settings",
            "type": "iframe",
            "permissions": ["admin.can_edit"],
        }
    ]

Troubleshooting
---------------

**Plugin not appearing in sidebar:**
- Check that the webserver was restarted after adding the plugin
- Verify the plugin is in the correct directory (``$AIRFLOW_HOME/plugins/``)
- Check the Airflow logs for plugin loading errors
- Ensure you have the required permissions

**UI not loading:**
- Verify the FastAPI app is serving static files correctly
- Check the ``entry`` URL matches your FastAPI mount point
- Test the plugin URL directly (e.g., ``http://localhost:8080/my_plugin/ui``)

**Permission issues:**
- Check that the permissions specified in the ``ui`` configuration exist
- Verify your user has the required permissions
- Use the Airflow CLI to check permissions: ``airflow users list``

Support for Airflow 2 Plugins
=============================

Airflow 2 plugins using Flask AppBuilder are still supported with some limitations. However, for new development,
we strongly recommend using the new UI plugin system described above.

Legacy Flask AppBuilder plugins will:
- Continue to work through a compatibility layer
- Be accessible under ``/pluginsv2``
- Have limited integration with the new React UI

For the best user experience and future compatibility, migrate to the new UI plugin system when possible.
